// Accounting System JavaScript

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    loadData();
    updateDashboard();
    initializeCharts();
    setCurrentDate();
    initializeReportDates();

    // Add event listeners
    document.getElementById('income-form').addEventListener('submit', addIncome);
    document.getElementById('expense-form').addEventListener('submit', addExpense);

    // Add change listeners for invoice items calculation
    document.addEventListener('change', function(e) {
        if (e.target.classList.contains('item-quantity') || e.target.classList.contains('item-price')) {
            calculateItemTotal(e.target);
        }
    });
});

// Data Management Functions
function loadData() {
    // Update displays
    displayIncomeList();
    displayExpenseList();
    displayInvoiceList();
}

// Navigation Functions
function showSection(sectionName) {
    // Hide all sections
    const sections = document.querySelectorAll('.content-section');
    sections.forEach(section => {
        section.style.display = 'none';
    });
    
    // Show selected section
    document.getElementById(sectionName + '-section').style.display = 'block';
    
    // Update navigation
    const navLinks = document.querySelectorAll('.nav-link');
    navLinks.forEach(link => {
        link.classList.remove('active');
    });
    
    // Add active class to clicked nav item
    event.target.classList.add('active');
    
    // Update dashboard if dashboard is selected
    if (sectionName === 'dashboard') {
        updateDashboard();
        updateCharts();
    }
}

// Income Management Functions
function addIncome(event) {
    event.preventDefault();

    const description = document.getElementById('income-description').value;
    const amount = parseFloat(document.getElementById('income-amount').value);
    const category = document.getElementById('income-category').value;
    const date = document.getElementById('income-date').value;

    if (!description || !amount || !category || !date) {
        showAlert('يرجى ملء جميع الحقول', 'danger');
        return;
    }

    const income = {
        description: description,
        amount: amount,
        category: category,
        date: date
    };

    if (db.addIncome(income)) {
        displayIncomeList();
        updateDashboard();
        updateCharts();

        // Reset form
        document.getElementById('income-form').reset();
        setCurrentDate();

        showAlert('تم إضافة الإيراد بنجاح', 'success');
    } else {
        showAlert('حدث خطأ في إضافة الإيراد', 'danger');
    }
}

function displayIncomeList() {
    const tbody = document.getElementById('income-table-body');
    tbody.innerHTML = '';

    const incomeData = db.getIncome();

    // Sort by date (newest first)
    const sortedIncome = incomeData.sort((a, b) => new Date(b.date) - new Date(a.date));

    sortedIncome.forEach(income => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${formatDate(income.date)}</td>
            <td>${income.description}</td>
            <td><span class="badge bg-primary">${income.category}</span></td>
            <td class="text-currency">${formatCurrency(income.amount)}</td>
            <td>
                <button class="btn btn-sm btn-warning" onclick="editIncome('${income.id}')">
                    <i class="fas fa-edit"></i>
                </button>
                <button class="btn btn-sm btn-danger" onclick="deleteIncome('${income.id}')">
                    <i class="fas fa-trash"></i>
                </button>
            </td>
        `;
        tbody.appendChild(row);
    });
}

function editIncome(id) {
    const incomeData = db.getIncome();
    const income = incomeData.find(item => item.id === id);
    if (income) {
        document.getElementById('income-description').value = income.description;
        document.getElementById('income-amount').value = income.amount;
        document.getElementById('income-category').value = income.category;
        document.getElementById('income-date').value = income.date;

        // Remove the old entry
        deleteIncome(id);
    }
}

function deleteIncome(id) {
    if (confirm('هل أنت متأكد من حذف هذا الإيراد؟')) {
        if (db.deleteIncome(id)) {
            displayIncomeList();
            updateDashboard();
            updateCharts();
            showAlert('تم حذف الإيراد بنجاح', 'success');
        } else {
            showAlert('حدث خطأ في حذف الإيراد', 'danger');
        }
    }
}

// Expense Management Functions
function addExpense(event) {
    event.preventDefault();

    const description = document.getElementById('expense-description').value;
    const amount = parseFloat(document.getElementById('expense-amount').value);
    const category = document.getElementById('expense-category').value;
    const date = document.getElementById('expense-date').value;
    const notes = document.getElementById('expense-notes').value;

    if (!description || !amount || !category || !date) {
        showAlert('يرجى ملء جميع الحقول المطلوبة', 'danger');
        return;
    }

    const expense = {
        description: description,
        amount: amount,
        category: category,
        date: date,
        notes: notes
    };

    if (db.addExpense(expense)) {
        displayExpenseList();
        updateDashboard();
        updateCharts();

        // Reset form
        document.getElementById('expense-form').reset();
        setCurrentDate();

        showAlert('تم إضافة المصروف بنجاح', 'success');
    } else {
        showAlert('حدث خطأ في إضافة المصروف', 'danger');
    }
}

function displayExpenseList() {
    const tbody = document.getElementById('expense-table-body');
    tbody.innerHTML = '';

    const expenseData = db.getExpenses();

    // Sort by date (newest first)
    const sortedExpenses = expenseData.sort((a, b) => new Date(b.date) - new Date(a.date));

    sortedExpenses.forEach(expense => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${formatDate(expense.date)}</td>
            <td>${expense.description}</td>
            <td><span class="badge bg-danger">${expense.category}</span></td>
            <td class="text-expense">${formatCurrency(expense.amount)}</td>
            <td>
                <button class="btn btn-sm btn-warning" onclick="editExpense('${expense.id}')">
                    <i class="fas fa-edit"></i>
                </button>
                <button class="btn btn-sm btn-danger" onclick="deleteExpense('${expense.id}')">
                    <i class="fas fa-trash"></i>
                </button>
            </td>
        `;
        tbody.appendChild(row);
    });
}

function editExpense(id) {
    const expenseData = db.getExpenses();
    const expense = expenseData.find(item => item.id === id);
    if (expense) {
        document.getElementById('expense-description').value = expense.description;
        document.getElementById('expense-amount').value = expense.amount;
        document.getElementById('expense-category').value = expense.category;
        document.getElementById('expense-date').value = expense.date;
        document.getElementById('expense-notes').value = expense.notes || '';

        // Remove the old entry
        deleteExpense(id);
    }
}

function deleteExpense(id) {
    if (confirm('هل أنت متأكد من حذف هذا المصروف؟')) {
        if (db.deleteExpense(id)) {
            displayExpenseList();
            updateDashboard();
            updateCharts();
            showAlert('تم حذف المصروف بنجاح', 'success');
        } else {
            showAlert('حدث خطأ في حذف المصروف', 'danger');
        }
    }
}

// Dashboard Functions
function updateDashboard() {
    const totalIncome = db.getTotalIncome();
    const totalExpenses = db.getTotalExpenses();
    const netProfit = db.getNetProfit();
    const totalInvoices = db.getInvoices().length;

    document.getElementById('total-income').textContent = formatCurrency(totalIncome);
    document.getElementById('total-expenses').textContent = formatCurrency(totalExpenses);
    document.getElementById('net-profit').textContent = formatCurrency(netProfit);
    document.getElementById('total-invoices').textContent = totalInvoices;

    // Update profit card color based on value
    const profitCard = document.getElementById('net-profit').closest('.card');
    if (netProfit >= 0) {
        profitCard.className = 'card bg-success text-white';
    } else {
        profitCard.className = 'card bg-danger text-white';
    }
}

// Chart Functions
function initializeCharts() {
    initializeMonthlyChart();
    initializeExpensesChart();
}

function initializeMonthlyChart() {
    const ctx = document.getElementById('monthlyChart').getContext('2d');
    
    // Get monthly data for the last 6 months
    const monthlyData = getMonthlyData();
    
    new Chart(ctx, {
        type: 'line',
        data: {
            labels: monthlyData.labels,
            datasets: [{
                label: 'الإيرادات',
                data: monthlyData.income,
                borderColor: '#28a745',
                backgroundColor: 'rgba(40, 167, 69, 0.1)',
                tension: 0.4
            }, {
                label: 'المصروفات',
                data: monthlyData.expenses,
                borderColor: '#dc3545',
                backgroundColor: 'rgba(220, 53, 69, 0.1)',
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'top',
                }
            },
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
}

function initializeExpensesChart() {
    const ctx = document.getElementById('expensesChart').getContext('2d');
    
    // Get expense categories data
    const categoryData = getExpenseCategoryData();
    
    new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: categoryData.labels,
            datasets: [{
                data: categoryData.data,
                backgroundColor: [
                    '#FF6384',
                    '#36A2EB',
                    '#FFCE56',
                    '#4BC0C0',
                    '#9966FF',
                    '#FF9F40'
                ]
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'bottom',
                }
            }
        }
    });
}

function updateCharts() {
    // Destroy existing charts and recreate them
    Chart.getChart('monthlyChart')?.destroy();
    Chart.getChart('expensesChart')?.destroy();
    
    initializeCharts();
}

// Utility Functions
function formatCurrency(amount) {
    return new Intl.NumberFormat('ar-SA', {
        style: 'currency',
        currency: 'SAR',
        minimumFractionDigits: 2
    }).format(amount);
}

function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-SA');
}

function setCurrentDate() {
    const today = new Date().toISOString().split('T')[0];
    const dateInputs = document.querySelectorAll('input[type="date"]');
    dateInputs.forEach(input => {
        if (!input.value) {
            input.value = today;
        }
    });
}

function showAlert(message, type) {
    // Create alert element
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    // Insert at the top of the container
    const container = document.querySelector('.container-fluid');
    container.insertBefore(alertDiv, container.firstChild);
    
    // Auto-dismiss after 3 seconds
    setTimeout(() => {
        alertDiv.remove();
    }, 3000);
}

function getMonthlyData() {
    const incomeData = db.getIncome();
    const expenseData = db.getExpenses();

    // Get last 6 months
    const months = [];
    const monthNames = ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
                       'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'];

    const now = new Date();
    for (let i = 5; i >= 0; i--) {
        const date = new Date(now.getFullYear(), now.getMonth() - i, 1);
        months.push({
            year: date.getFullYear(),
            month: date.getMonth(),
            name: monthNames[date.getMonth()]
        });
    }

    const monthlyIncome = months.map(month => {
        return incomeData
            .filter(item => {
                const itemDate = new Date(item.date);
                return itemDate.getFullYear() === month.year && itemDate.getMonth() === month.month;
            })
            .reduce((sum, item) => sum + parseFloat(item.amount), 0);
    });

    const monthlyExpenses = months.map(month => {
        return expenseData
            .filter(item => {
                const itemDate = new Date(item.date);
                return itemDate.getFullYear() === month.year && itemDate.getMonth() === month.month;
            })
            .reduce((sum, item) => sum + parseFloat(item.amount), 0);
    });

    return {
        labels: months.map(m => m.name),
        income: monthlyIncome,
        expenses: monthlyExpenses
    };
}

function getExpenseCategoryData() {
    const expenseData = db.getExpenses();
    const categories = {};

    expenseData.forEach(expense => {
        if (categories[expense.category]) {
            categories[expense.category] += parseFloat(expense.amount);
        } else {
            categories[expense.category] = parseFloat(expense.amount);
        }
    });

    return {
        labels: Object.keys(categories),
        data: Object.values(categories)
    };
}

// Invoice Management Functions
function showCreateInvoiceModal() {
    // Reset form
    document.getElementById('invoice-form').reset();
    setCurrentDate();

    // Clear items table
    const tbody = document.getElementById('invoice-items-body');
    tbody.innerHTML = `
        <tr>
            <td><input type="text" class="form-control item-description" placeholder="وصف الخدمة/المنتج"></td>
            <td><input type="number" class="form-control item-quantity" value="1" min="1" onchange="calculateItemTotal(this)"></td>
            <td><input type="number" class="form-control item-price" step="0.01" placeholder="0.00" onchange="calculateItemTotal(this)"></td>
            <td class="item-total">0.00</td>
            <td><button type="button" class="btn btn-sm btn-danger" onclick="removeInvoiceItem(this)"><i class="fas fa-trash"></i></button></td>
        </tr>
    `;

    calculateInvoiceTotal();

    // Show modal
    const modal = new bootstrap.Modal(document.getElementById('invoiceModal'));
    modal.show();
}

function addInvoiceItem() {
    const tbody = document.getElementById('invoice-items-body');
    const newRow = document.createElement('tr');
    newRow.innerHTML = `
        <td><input type="text" class="form-control item-description" placeholder="وصف الخدمة/المنتج"></td>
        <td><input type="number" class="form-control item-quantity" value="1" min="1" onchange="calculateItemTotal(this)"></td>
        <td><input type="number" class="form-control item-price" step="0.01" placeholder="0.00" onchange="calculateItemTotal(this)"></td>
        <td class="item-total">0.00</td>
        <td><button type="button" class="btn btn-sm btn-danger" onclick="removeInvoiceItem(this)"><i class="fas fa-trash"></i></button></td>
    `;
    tbody.appendChild(newRow);
}

function removeInvoiceItem(button) {
    const row = button.closest('tr');
    const tbody = document.getElementById('invoice-items-body');

    // Don't remove if it's the last row
    if (tbody.children.length > 1) {
        row.remove();
        calculateInvoiceTotal();
    }
}

function calculateItemTotal(input) {
    const row = input.closest('tr');
    const quantity = parseFloat(row.querySelector('.item-quantity').value) || 0;
    const price = parseFloat(row.querySelector('.item-price').value) || 0;
    const total = quantity * price;

    row.querySelector('.item-total').textContent = total.toFixed(2);
    calculateInvoiceTotal();
}

function calculateInvoiceTotal() {
    const itemTotals = document.querySelectorAll('.item-total');
    let subtotal = 0;

    itemTotals.forEach(cell => {
        subtotal += parseFloat(cell.textContent) || 0;
    });

    const taxRate = 0.15; // 15% tax
    const tax = subtotal * taxRate;
    const total = subtotal + tax;

    document.getElementById('invoice-subtotal').textContent = formatCurrency(subtotal);
    document.getElementById('invoice-tax').textContent = formatCurrency(tax);
    document.getElementById('invoice-total').textContent = formatCurrency(total);
}

function saveInvoice() {
    const customer = document.getElementById('invoice-customer').value;
    const date = document.getElementById('invoice-date').value;
    const phone = document.getElementById('invoice-phone').value;
    const dueDate = document.getElementById('invoice-due-date').value;
    const address = document.getElementById('invoice-address').value;
    const notes = document.getElementById('invoice-notes').value;

    if (!customer || !date) {
        showAlert('يرجى ملء الحقول المطلوبة', 'danger');
        return;
    }

    // Collect items
    const items = [];
    const rows = document.querySelectorAll('#invoice-items-body tr');

    rows.forEach(row => {
        const description = row.querySelector('.item-description').value;
        const quantity = parseFloat(row.querySelector('.item-quantity').value) || 0;
        const price = parseFloat(row.querySelector('.item-price').value) || 0;

        if (description && quantity > 0 && price > 0) {
            items.push({
                description: description,
                quantity: quantity,
                price: price,
                total: quantity * price
            });
        }
    });

    if (items.length === 0) {
        showAlert('يرجى إضافة عنصر واحد على الأقل للفاتورة', 'danger');
        return;
    }

    const subtotal = items.reduce((sum, item) => sum + item.total, 0);
    const tax = subtotal * 0.15;
    const total = subtotal + tax;

    const invoice = {
        customer: customer,
        phone: phone,
        address: address,
        date: date,
        dueDate: dueDate,
        items: items,
        subtotal: subtotal,
        tax: tax,
        total: total,
        notes: notes,
        status: 'pending'
    };

    if (db.addInvoice(invoice)) {
        displayInvoiceList();
        updateDashboard();

        // Close modal
        const modal = bootstrap.Modal.getInstance(document.getElementById('invoiceModal'));
        modal.hide();

        showAlert('تم إنشاء الفاتورة بنجاح', 'success');
    } else {
        showAlert('حدث خطأ في إنشاء الفاتورة', 'danger');
    }
}

function displayInvoiceList() {
    const tbody = document.getElementById('invoice-table-body');
    tbody.innerHTML = '';

    const invoiceData = db.getInvoices();

    // Sort by date (newest first)
    const sortedInvoices = invoiceData.sort((a, b) => new Date(b.date) - new Date(a.date));

    sortedInvoices.forEach(invoice => {
        const row = document.createElement('tr');
        const statusClass = invoice.status === 'paid' ? 'status-paid' :
                           invoice.status === 'overdue' ? 'status-overdue' : 'status-pending';
        const statusText = invoice.status === 'paid' ? 'مدفوعة' :
                          invoice.status === 'overdue' ? 'متأخرة' : 'معلقة';

        row.innerHTML = `
            <td>${invoice.invoiceNumber}</td>
            <td>${invoice.customer}</td>
            <td>${formatDate(invoice.date)}</td>
            <td class="text-currency">${formatCurrency(invoice.total)}</td>
            <td><span class="${statusClass}">${statusText}</span></td>
            <td>
                <button class="btn btn-sm btn-info" onclick="viewInvoice('${invoice.id}')">
                    <i class="fas fa-eye"></i>
                </button>
                <button class="btn btn-sm btn-success" onclick="markAsPaid('${invoice.id}')">
                    <i class="fas fa-check"></i>
                </button>
                <button class="btn btn-sm btn-warning" onclick="printInvoice('${invoice.id}')">
                    <i class="fas fa-print"></i>
                </button>
                <button class="btn btn-sm btn-danger" onclick="deleteInvoice('${invoice.id}')">
                    <i class="fas fa-trash"></i>
                </button>
            </td>
        `;
        tbody.appendChild(row);
    });
}

function markAsPaid(id) {
    if (confirm('هل تريد تأكيد دفع هذه الفاتورة؟')) {
        if (db.updateInvoice(id, { status: 'paid' })) {
            displayInvoiceList();
            updateDashboard();
            showAlert('تم تحديث حالة الفاتورة إلى مدفوعة', 'success');
        }
    }
}

function deleteInvoice(id) {
    if (confirm('هل أنت متأكد من حذف هذه الفاتورة؟')) {
        if (db.deleteInvoice(id)) {
            displayInvoiceList();
            updateDashboard();
            showAlert('تم حذف الفاتورة بنجاح', 'success');
        }
    }
}

function viewInvoice(id) {
    // This will be implemented for viewing invoice details
    showAlert('عرض تفاصيل الفاتورة قيد التطوير', 'info');
}

function printInvoice(id) {
    // This will be implemented for printing invoices
    showAlert('طباعة الفاتورة قيد التطوير', 'info');
}

// Reports Management Functions
function generateReport() {
    const startDate = document.getElementById('report-start-date').value;
    const endDate = document.getElementById('report-end-date').value;
    const reportType = document.getElementById('report-type').value;

    if (!startDate || !endDate) {
        showAlert('يرجى تحديد تاريخ البداية والنهاية', 'warning');
        return;
    }

    if (new Date(startDate) > new Date(endDate)) {
        showAlert('تاريخ البداية يجب أن يكون قبل تاريخ النهاية', 'warning');
        return;
    }

    // Update summary cards
    updateReportSummary(startDate, endDate);

    // Generate report table based on type
    switch (reportType) {
        case 'summary':
            generateSummaryReport(startDate, endDate);
            break;
        case 'income':
            generateIncomeReport(startDate, endDate);
            break;
        case 'expenses':
            generateExpensesReport(startDate, endDate);
            break;
        case 'invoices':
            generateInvoicesReport(startDate, endDate);
            break;
    }

    // Update charts
    updateReportCharts(startDate, endDate);
}

function updateReportSummary(startDate, endDate) {
    const totalIncome = db.getTotalIncome(startDate, endDate);
    const totalExpenses = db.getTotalExpenses(startDate, endDate);
    const netProfit = totalIncome - totalExpenses;
    const invoices = db.getInvoices().filter(inv => {
        const invDate = new Date(inv.date);
        return invDate >= new Date(startDate) && invDate <= new Date(endDate);
    });

    document.getElementById('report-total-income').textContent = formatCurrency(totalIncome);
    document.getElementById('report-total-expenses').textContent = formatCurrency(totalExpenses);
    document.getElementById('report-net-profit').textContent = formatCurrency(netProfit);
    document.getElementById('report-invoice-count').textContent = invoices.length;

    // Update profit card color
    const profitCard = document.getElementById('report-net-profit').closest('.card');
    if (netProfit >= 0) {
        profitCard.classList.remove('expense');
        profitCard.classList.add('profit');
    } else {
        profitCard.classList.remove('profit');
        profitCard.classList.add('expense');
    }
}

function generateSummaryReport(startDate, endDate) {
    const income = db.getIncome().filter(item =>
        new Date(item.date) >= new Date(startDate) && new Date(item.date) <= new Date(endDate)
    );
    const expenses = db.getExpenses().filter(item =>
        new Date(item.date) >= new Date(startDate) && new Date(item.date) <= new Date(endDate)
    );

    document.getElementById('report-table-title').textContent = 'ملخص عام للفترة';

    const thead = document.getElementById('report-table-head');
    thead.innerHTML = `
        <tr>
            <th>النوع</th>
            <th>التصنيف</th>
            <th>عدد العمليات</th>
            <th>إجمالي المبلغ</th>
            <th>متوسط المبلغ</th>
        </tr>
    `;

    const tbody = document.getElementById('report-table-body');
    tbody.innerHTML = '';

    // Group income by category
    const incomeByCategory = {};
    income.forEach(item => {
        if (!incomeByCategory[item.category]) {
            incomeByCategory[item.category] = { count: 0, total: 0 };
        }
        incomeByCategory[item.category].count++;
        incomeByCategory[item.category].total += parseFloat(item.amount);
    });

    // Group expenses by category
    const expensesByCategory = {};
    expenses.forEach(item => {
        if (!expensesByCategory[item.category]) {
            expensesByCategory[item.category] = { count: 0, total: 0 };
        }
        expensesByCategory[item.category].count++;
        expensesByCategory[item.category].total += parseFloat(item.amount);
    });

    // Add income rows
    Object.keys(incomeByCategory).forEach(category => {
        const data = incomeByCategory[category];
        const row = document.createElement('tr');
        row.innerHTML = `
            <td><span class="badge bg-success">إيراد</span></td>
            <td>${category}</td>
            <td>${data.count}</td>
            <td class="text-currency">${formatCurrency(data.total)}</td>
            <td class="text-currency">${formatCurrency(data.total / data.count)}</td>
        `;
        tbody.appendChild(row);
    });

    // Add expense rows
    Object.keys(expensesByCategory).forEach(category => {
        const data = expensesByCategory[category];
        const row = document.createElement('tr');
        row.innerHTML = `
            <td><span class="badge bg-danger">مصروف</span></td>
            <td>${category}</td>
            <td>${data.count}</td>
            <td class="text-expense">${formatCurrency(data.total)}</td>
            <td class="text-expense">${formatCurrency(data.total / data.count)}</td>
        `;
        tbody.appendChild(row);
    });
}

function generateIncomeReport(startDate, endDate) {
    const income = db.getIncome().filter(item =>
        new Date(item.date) >= new Date(startDate) && new Date(item.date) <= new Date(endDate)
    );

    document.getElementById('report-table-title').textContent = 'تقرير الإيرادات';

    const thead = document.getElementById('report-table-head');
    thead.innerHTML = `
        <tr>
            <th>التاريخ</th>
            <th>الوصف</th>
            <th>التصنيف</th>
            <th>المبلغ</th>
        </tr>
    `;

    const tbody = document.getElementById('report-table-body');
    tbody.innerHTML = '';

    income.sort((a, b) => new Date(b.date) - new Date(a.date)).forEach(item => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${formatDate(item.date)}</td>
            <td>${item.description}</td>
            <td><span class="badge bg-primary">${item.category}</span></td>
            <td class="text-currency">${formatCurrency(item.amount)}</td>
        `;
        tbody.appendChild(row);
    });
}

function generateExpensesReport(startDate, endDate) {
    const expenses = db.getExpenses().filter(item =>
        new Date(item.date) >= new Date(startDate) && new Date(item.date) <= new Date(endDate)
    );

    document.getElementById('report-table-title').textContent = 'تقرير المصروفات';

    const thead = document.getElementById('report-table-head');
    thead.innerHTML = `
        <tr>
            <th>التاريخ</th>
            <th>الوصف</th>
            <th>التصنيف</th>
            <th>المبلغ</th>
            <th>ملاحظات</th>
        </tr>
    `;

    const tbody = document.getElementById('report-table-body');
    tbody.innerHTML = '';

    expenses.sort((a, b) => new Date(b.date) - new Date(a.date)).forEach(item => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${formatDate(item.date)}</td>
            <td>${item.description}</td>
            <td><span class="badge bg-danger">${item.category}</span></td>
            <td class="text-expense">${formatCurrency(item.amount)}</td>
            <td>${item.notes || '-'}</td>
        `;
        tbody.appendChild(row);
    });
}

function generateInvoicesReport(startDate, endDate) {
    const invoices = db.getInvoices().filter(item =>
        new Date(item.date) >= new Date(startDate) && new Date(item.date) <= new Date(endDate)
    );

    document.getElementById('report-table-title').textContent = 'تقرير الفواتير';

    const thead = document.getElementById('report-table-head');
    thead.innerHTML = `
        <tr>
            <th>رقم الفاتورة</th>
            <th>العميل</th>
            <th>التاريخ</th>
            <th>تاريخ الاستحقاق</th>
            <th>المبلغ الإجمالي</th>
            <th>الحالة</th>
        </tr>
    `;

    const tbody = document.getElementById('report-table-body');
    tbody.innerHTML = '';

    invoices.sort((a, b) => new Date(b.date) - new Date(a.date)).forEach(item => {
        const row = document.createElement('tr');
        const statusClass = item.status === 'paid' ? 'status-paid' :
                           item.status === 'overdue' ? 'status-overdue' : 'status-pending';
        const statusText = item.status === 'paid' ? 'مدفوعة' :
                          item.status === 'overdue' ? 'متأخرة' : 'معلقة';

        row.innerHTML = `
            <td>${item.invoiceNumber}</td>
            <td>${item.customer}</td>
            <td>${formatDate(item.date)}</td>
            <td>${item.dueDate ? formatDate(item.dueDate) : '-'}</td>
            <td class="text-currency">${formatCurrency(item.total)}</td>
            <td><span class="${statusClass}">${statusText}</span></td>
        `;
        tbody.appendChild(row);
    });
}

function updateReportCharts(startDate, endDate) {
    // Destroy existing charts
    Chart.getChart('reportTrendChart')?.destroy();
    Chart.getChart('reportIncomeChart')?.destroy();

    // Create trend chart
    const trendCtx = document.getElementById('reportTrendChart').getContext('2d');
    const trendData = getTrendData(startDate, endDate);

    new Chart(trendCtx, {
        type: 'line',
        data: {
            labels: trendData.labels,
            datasets: [{
                label: 'الإيرادات',
                data: trendData.income,
                borderColor: '#28a745',
                backgroundColor: 'rgba(40, 167, 69, 0.1)',
                tension: 0.4
            }, {
                label: 'المصروفات',
                data: trendData.expenses,
                borderColor: '#dc3545',
                backgroundColor: 'rgba(220, 53, 69, 0.1)',
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'top',
                }
            },
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });

    // Create income distribution chart
    const incomeCtx = document.getElementById('reportIncomeChart').getContext('2d');
    const incomeData = getIncomeCategoryData(startDate, endDate);

    new Chart(incomeCtx, {
        type: 'doughnut',
        data: {
            labels: incomeData.labels,
            datasets: [{
                data: incomeData.data,
                backgroundColor: [
                    '#28a745',
                    '#17a2b8',
                    '#6f42c1',
                    '#fd7e14',
                    '#6c757d',
                    '#e83e8c'
                ]
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'bottom',
                }
            }
        }
    });
}

function getTrendData(startDate, endDate) {
    const income = db.getIncome().filter(item =>
        new Date(item.date) >= new Date(startDate) && new Date(item.date) <= new Date(endDate)
    );
    const expenses = db.getExpenses().filter(item =>
        new Date(item.date) >= new Date(startDate) && new Date(item.date) <= new Date(endDate)
    );

    // Group by month
    const monthlyData = {};

    income.forEach(item => {
        const month = new Date(item.date).toISOString().slice(0, 7); // YYYY-MM
        if (!monthlyData[month]) {
            monthlyData[month] = { income: 0, expenses: 0 };
        }
        monthlyData[month].income += parseFloat(item.amount);
    });

    expenses.forEach(item => {
        const month = new Date(item.date).toISOString().slice(0, 7); // YYYY-MM
        if (!monthlyData[month]) {
            monthlyData[month] = { income: 0, expenses: 0 };
        }
        monthlyData[month].expenses += parseFloat(item.amount);
    });

    const sortedMonths = Object.keys(monthlyData).sort();

    return {
        labels: sortedMonths.map(month => {
            const date = new Date(month + '-01');
            return date.toLocaleDateString('ar-SA', { year: 'numeric', month: 'long' });
        }),
        income: sortedMonths.map(month => monthlyData[month].income),
        expenses: sortedMonths.map(month => monthlyData[month].expenses)
    };
}

function getIncomeCategoryData(startDate, endDate) {
    const income = db.getIncome().filter(item =>
        new Date(item.date) >= new Date(startDate) && new Date(item.date) <= new Date(endDate)
    );

    const categories = {};
    income.forEach(item => {
        if (categories[item.category]) {
            categories[item.category] += parseFloat(item.amount);
        } else {
            categories[item.category] = parseFloat(item.amount);
        }
    });

    return {
        labels: Object.keys(categories),
        data: Object.values(categories)
    };
}

// Export Functions
function exportToExcel() {
    const table = document.getElementById('report-table');
    const reportTitle = document.getElementById('report-table-title').textContent;

    // Create workbook
    const wb = XLSX.utils.book_new();
    const ws = XLSX.utils.table_to_sheet(table);

    // Add worksheet to workbook
    XLSX.utils.book_append_sheet(wb, ws, reportTitle);

    // Generate filename with current date
    const filename = `${reportTitle}_${new Date().toISOString().slice(0, 10)}.xlsx`;

    // Save file
    XLSX.writeFile(wb, filename);

    showAlert('تم تصدير التقرير إلى Excel بنجاح', 'success');
}

function exportToPDF() {
    const { jsPDF } = window.jspdf;
    const doc = new jsPDF();

    // Add Arabic font support (if available)
    doc.setFont('helvetica');

    const reportTitle = document.getElementById('report-table-title').textContent;
    const startDate = document.getElementById('report-start-date').value;
    const endDate = document.getElementById('report-end-date').value;

    // Add title
    doc.setFontSize(16);
    doc.text(reportTitle, 20, 20);

    // Add date range
    doc.setFontSize(12);
    doc.text(`من ${startDate} إلى ${endDate}`, 20, 35);

    // Add table
    const table = document.getElementById('report-table');
    const rows = [];

    // Get headers
    const headers = [];
    table.querySelectorAll('thead th').forEach(th => {
        headers.push(th.textContent);
    });
    rows.push(headers);

    // Get data rows
    table.querySelectorAll('tbody tr').forEach(tr => {
        const row = [];
        tr.querySelectorAll('td').forEach(td => {
            row.push(td.textContent);
        });
        rows.push(row);
    });

    // Add table to PDF
    doc.autoTable({
        head: [headers],
        body: rows.slice(1),
        startY: 50,
        styles: {
            fontSize: 8,
            cellPadding: 2
        }
    });

    // Generate filename
    const filename = `${reportTitle}_${new Date().toISOString().slice(0, 10)}.pdf`;

    // Save PDF
    doc.save(filename);

    showAlert('تم تصدير التقرير إلى PDF بنجاح', 'success');
}

// Initialize report dates on page load
function initializeReportDates() {
    const today = new Date();
    const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);

    document.getElementById('report-start-date').value = firstDayOfMonth.toISOString().slice(0, 10);
    document.getElementById('report-end-date').value = today.toISOString().slice(0, 10);
}
