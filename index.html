<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام المحاسبة المتكامل</title>
    
    <!-- Bootstrap 5 RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    
    <!-- Font Awesome Icons -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Chart.js for graphs -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <!-- SheetJS for Excel export -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>

    <!-- jsPDF for PDF export -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf-autotable/3.5.31/jspdf.plugin.autotable.min.js"></script>

    <!-- Custom CSS -->
    <link href="css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg">
        <div class="container-fluid">
            <a class="navbar-brand floating" href="#">
                <i class="fas fa-calculator"></i>
                نظام المحاسبة المتكامل
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="#" onclick="showSection('dashboard')">
                            <i class="fas fa-tachometer-alt me-1"></i>
                            لوحة التحكم
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" onclick="showSection('income')">
                            <i class="fas fa-plus-circle me-1"></i>
                            الإيرادات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" onclick="showSection('expenses')">
                            <i class="fas fa-minus-circle me-1"></i>
                            المصروفات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" onclick="showSection('invoices')">
                            <i class="fas fa-file-invoice me-1"></i>
                            الفواتير
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" onclick="showSection('reports')">
                            <i class="fas fa-chart-bar me-1"></i>
                            التقارير
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container-fluid mt-4">
        <!-- Dashboard Section -->
        <div id="dashboard-section" class="content-section">
            <div class="row">
                <div class="col-12">
                    <h2 class="mb-4">
                        <i class="fas fa-tachometer-alt me-2"></i>
                        لوحة التحكم
                    </h2>
                </div>
            </div>
            
            <!-- Summary Cards -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-success text-white glow">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">إجمالي الإيرادات</h6>
                                    <h4 id="total-income">0 ر.س</h4>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-arrow-up fa-2x floating"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="card bg-danger text-white glow">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">إجمالي المصروفات</h6>
                                    <h4 id="total-expenses">0 ر.س</h4>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-arrow-down fa-2x floating"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-3">
                    <div class="card bg-info text-white glow">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">صافي الربح</h6>
                                    <h4 id="net-profit">0 ر.س</h4>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-chart-line fa-2x floating"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-3">
                    <div class="card bg-warning text-white glow">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">عدد الفواتير</h6>
                                    <h4 id="total-invoices">0</h4>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-file-invoice fa-2x floating"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Charts Row -->
            <div class="row">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5>الإيرادات والمصروفات الشهرية</h5>
                        </div>
                        <div class="card-body">
                            <canvas id="monthlyChart"></canvas>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5>توزيع المصروفات</h5>
                        </div>
                        <div class="card-body">
                            <canvas id="expensesChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Income Section -->
        <div id="income-section" class="content-section" style="display: none;">
            <div class="row">
                <div class="col-12">
                    <h2 class="mb-4">
                        <i class="fas fa-plus-circle me-2"></i>
                        إدارة الإيرادات
                    </h2>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h5>إضافة إيراد جديد</h5>
                        </div>
                        <div class="card-body">
                            <form id="income-form">
                                <div class="mb-3">
                                    <label class="form-label">الوصف</label>
                                    <input type="text" class="form-control" id="income-description" required>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">المبلغ (ر.س)</label>
                                    <input type="number" class="form-control" id="income-amount" step="0.01" required>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">التصنيف</label>
                                    <select class="form-control" id="income-category" required>
                                        <option value="">اختر التصنيف</option>
                                        <option value="مبيعات">مبيعات</option>
                                        <option value="خدمات">خدمات</option>
                                        <option value="استثمارات">استثمارات</option>
                                        <option value="أخرى">أخرى</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">التاريخ</label>
                                    <input type="date" class="form-control" id="income-date" required>
                                </div>
                                <button type="submit" class="btn btn-success w-100 ripple">
                                    <i class="fas fa-plus me-1"></i>
                                    إضافة الإيراد
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h5>قائمة الإيرادات</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>التاريخ</th>
                                            <th>الوصف</th>
                                            <th>التصنيف</th>
                                            <th>المبلغ</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody id="income-table-body">
                                        <!-- Income entries will be populated here -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Expenses Section -->
        <div id="expenses-section" class="content-section" style="display: none;">
            <div class="row">
                <div class="col-12">
                    <h2 class="mb-4">
                        <i class="fas fa-minus-circle me-2"></i>
                        إدارة المصروفات
                    </h2>
                </div>
            </div>

            <div class="row">
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h5>إضافة مصروف جديد</h5>
                        </div>
                        <div class="card-body">
                            <form id="expense-form">
                                <div class="mb-3">
                                    <label class="form-label">الوصف</label>
                                    <input type="text" class="form-control" id="expense-description" required>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">المبلغ (ر.س)</label>
                                    <input type="number" class="form-control" id="expense-amount" step="0.01" required>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">التصنيف</label>
                                    <select class="form-control" id="expense-category" required>
                                        <option value="">اختر التصنيف</option>
                                        <option value="مكتب وإدارة">مكتب وإدارة</option>
                                        <option value="مواصلات">مواصلات</option>
                                        <option value="طعام وضيافة">طعام وضيافة</option>
                                        <option value="تسويق وإعلان">تسويق وإعلان</option>
                                        <option value="رواتب">رواتب</option>
                                        <option value="إيجار">إيجار</option>
                                        <option value="كهرباء وماء">كهرباء وماء</option>
                                        <option value="صيانة">صيانة</option>
                                        <option value="أخرى">أخرى</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">التاريخ</label>
                                    <input type="date" class="form-control" id="expense-date" required>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">ملاحظات</label>
                                    <textarea class="form-control" id="expense-notes" rows="3"></textarea>
                                </div>
                                <button type="submit" class="btn btn-danger w-100 ripple">
                                    <i class="fas fa-plus me-1"></i>
                                    إضافة المصروف
                                </button>
                            </form>
                        </div>
                    </div>
                </div>

                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h5>قائمة المصروفات</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>التاريخ</th>
                                            <th>الوصف</th>
                                            <th>التصنيف</th>
                                            <th>المبلغ</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody id="expense-table-body">
                                        <!-- Expense entries will be populated here -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Invoices Section -->
        <div id="invoices-section" class="content-section" style="display: none;">
            <div class="row">
                <div class="col-12">
                    <h2 class="mb-4">
                        <i class="fas fa-file-invoice me-2"></i>
                        إدارة الفواتير
                    </h2>
                </div>
            </div>

            <div class="row mb-3">
                <div class="col-12">
                    <button class="btn btn-primary ripple" onclick="showCreateInvoiceModal()">
                        <i class="fas fa-plus me-1"></i>
                        إنشاء فاتورة جديدة
                    </button>
                </div>
            </div>

            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5>قائمة الفواتير</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>رقم الفاتورة</th>
                                            <th>العميل</th>
                                            <th>التاريخ</th>
                                            <th>المبلغ الإجمالي</th>
                                            <th>الحالة</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody id="invoice-table-body">
                                        <!-- Invoice entries will be populated here -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Reports Section -->
        <div id="reports-section" class="content-section" style="display: none;">
            <div class="row">
                <div class="col-12">
                    <h2 class="mb-4">
                        <i class="fas fa-chart-bar me-2"></i>
                        التقارير والإحصائيات
                    </h2>
                </div>
            </div>

            <!-- Filter Controls -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <label class="form-label">من تاريخ</label>
                    <input type="date" class="form-control" id="report-start-date">
                </div>
                <div class="col-md-3">
                    <label class="form-label">إلى تاريخ</label>
                    <input type="date" class="form-control" id="report-end-date">
                </div>
                <div class="col-md-3">
                    <label class="form-label">نوع التقرير</label>
                    <select class="form-control" id="report-type">
                        <option value="summary">ملخص عام</option>
                        <option value="income">تقرير الإيرادات</option>
                        <option value="expenses">تقرير المصروفات</option>
                        <option value="invoices">تقرير الفواتير</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <button class="btn btn-primary mt-4" onclick="generateReport()">
                        <i class="fas fa-chart-line me-1"></i>
                        إنشاء التقرير
                    </button>
                </div>
            </div>

            <!-- Report Summary Cards -->
            <div class="row mb-4" id="report-summary">
                <div class="col-md-3">
                    <div class="card dashboard-card income">
                        <div class="card-body">
                            <h6 class="card-title">إجمالي الإيرادات</h6>
                            <h4 id="report-total-income">0 ر.س</h4>
                            <small class="text-muted">للفترة المحددة</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card dashboard-card expense">
                        <div class="card-body">
                            <h6 class="card-title">إجمالي المصروفات</h6>
                            <h4 id="report-total-expenses">0 ر.س</h4>
                            <small class="text-muted">للفترة المحددة</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card dashboard-card profit">
                        <div class="card-body">
                            <h6 class="card-title">صافي الربح</h6>
                            <h4 id="report-net-profit">0 ر.س</h4>
                            <small class="text-muted">للفترة المحددة</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card dashboard-card invoice">
                        <div class="card-body">
                            <h6 class="card-title">عدد الفواتير</h6>
                            <h4 id="report-invoice-count">0</h4>
                            <small class="text-muted">للفترة المحددة</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Charts Row -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5>الاتجاه الزمني للإيرادات والمصروفات</h5>
                        </div>
                        <div class="card-body">
                            <canvas id="reportTrendChart"></canvas>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5>توزيع الإيرادات حسب التصنيف</h5>
                        </div>
                        <div class="card-body">
                            <canvas id="reportIncomeChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Detailed Report Table -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 id="report-table-title">تفاصيل التقرير</h5>
                            <div>
                                <button class="btn btn-success btn-sm" onclick="exportToExcel()">
                                    <i class="fas fa-file-excel me-1"></i>
                                    تصدير Excel
                                </button>
                                <button class="btn btn-danger btn-sm" onclick="exportToPDF()">
                                    <i class="fas fa-file-pdf me-1"></i>
                                    تصدير PDF
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped" id="report-table">
                                    <thead id="report-table-head">
                                        <!-- Table headers will be populated dynamically -->
                                    </thead>
                                    <tbody id="report-table-body">
                                        <!-- Table data will be populated dynamically -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Invoice Creation Modal -->
    <div class="modal fade" id="invoiceModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">إنشاء فاتورة جديدة</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="invoice-form">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">اسم العميل</label>
                                    <input type="text" class="form-control" id="invoice-customer" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">تاريخ الفاتورة</label>
                                    <input type="date" class="form-control" id="invoice-date" required>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">هاتف العميل</label>
                                    <input type="tel" class="form-control" id="invoice-phone">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">تاريخ الاستحقاق</label>
                                    <input type="date" class="form-control" id="invoice-due-date">
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">عنوان العميل</label>
                            <textarea class="form-control" id="invoice-address" rows="2"></textarea>
                        </div>

                        <hr>
                        <h6>عناصر الفاتورة</h6>

                        <div class="table-responsive">
                            <table class="table table-bordered" id="invoice-items-table">
                                <thead>
                                    <tr>
                                        <th>الوصف</th>
                                        <th>الكمية</th>
                                        <th>السعر</th>
                                        <th>الإجمالي</th>
                                        <th>إجراء</th>
                                    </tr>
                                </thead>
                                <tbody id="invoice-items-body">
                                    <tr>
                                        <td><input type="text" class="form-control item-description" placeholder="وصف الخدمة/المنتج"></td>
                                        <td><input type="number" class="form-control item-quantity" value="1" min="1"></td>
                                        <td><input type="number" class="form-control item-price" step="0.01" placeholder="0.00"></td>
                                        <td class="item-total">0.00</td>
                                        <td><button type="button" class="btn btn-sm btn-danger" onclick="removeInvoiceItem(this)"><i class="fas fa-trash"></i></button></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <button type="button" class="btn btn-secondary mb-3" onclick="addInvoiceItem()">
                            <i class="fas fa-plus me-1"></i>
                            إضافة عنصر
                        </button>

                        <div class="row">
                            <div class="col-md-6 ms-auto">
                                <table class="table">
                                    <tr>
                                        <td><strong>المجموع الفرعي:</strong></td>
                                        <td class="text-end" id="invoice-subtotal">0.00 ر.س</td>
                                    </tr>
                                    <tr>
                                        <td><strong>الضريبة (15%):</strong></td>
                                        <td class="text-end" id="invoice-tax">0.00 ر.س</td>
                                    </tr>
                                    <tr class="table-primary">
                                        <td><strong>الإجمالي:</strong></td>
                                        <td class="text-end" id="invoice-total">0.00 ر.س</td>
                                    </tr>
                                </table>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">ملاحظات</label>
                            <textarea class="form-control" id="invoice-notes" rows="3"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" onclick="saveInvoice()">حفظ الفاتورة</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Custom JavaScript -->
    <script src="js/database.js"></script>
    <script src="js/app.js"></script>
</body>
</html>
