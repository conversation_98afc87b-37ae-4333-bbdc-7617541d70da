/* Modern Professional Accounting System CSS */

/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');

/* CSS Variables for Modern Design System */
:root {
    /* Primary Colors */
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    --warning-gradient: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    --danger-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
    --info-gradient: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);

    /* Neutral Colors */
    --bg-primary: #0f0f23;
    --bg-secondary: #1a1a2e;
    --bg-tertiary: #16213e;
    --surface: rgba(255, 255, 255, 0.05);
    --surface-hover: rgba(255, 255, 255, 0.1);
    --text-primary: #ffffff;
    --text-secondary: #a0a0a0;
    --text-muted: #6b7280;

    /* Glass Morphism */
    --glass-bg: rgba(255, 255, 255, 0.1);
    --glass-border: rgba(255, 255, 255, 0.2);
    --glass-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);

    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;

    /* Border Radius */
    --radius-sm: 8px;
    --radius-md: 12px;
    --radius-lg: 16px;
    --radius-xl: 24px;

    /* Shadows */
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.1);
    --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.15);
    --shadow-glow: 0 0 20px rgba(103, 126, 234, 0.3);
}

/* General Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: var(--bg-primary);
    background-image:
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%);
    color: var(--text-primary);
    line-height: 1.6;
    overflow-x: hidden;
    min-height: 100vh;
}

/* Modern Navigation */
.navbar {
    background: var(--glass-bg) !important;
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-bottom: 1px solid var(--glass-border);
    box-shadow: var(--glass-shadow);
    padding: var(--spacing-md) 0;
    position: sticky;
    top: 0;
    z-index: 1000;
}

.navbar-brand {
    font-weight: 800;
    font-size: 1.75rem;
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: none;
    letter-spacing: -0.5px;
}

.navbar-brand i {
    background: var(--success-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-left: var(--spacing-sm);
    filter: drop-shadow(0 0 10px rgba(79, 172, 254, 0.5));
}

.nav-link {
    font-weight: 500;
    color: var(--text-primary) !important;
    padding: var(--spacing-sm) var(--spacing-md) !important;
    margin: 0 var(--spacing-xs);
    border-radius: var(--radius-md);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: var(--primary-gradient);
    transition: left 0.3s ease;
    z-index: -1;
}

.nav-link:hover::before,
.nav-link.active::before {
    left: 0;
}

.nav-link:hover,
.nav-link.active {
    color: white !important;
    transform: translateY(-2px);
    box-shadow: var(--shadow-glow);
}

.nav-link i {
    margin-left: var(--spacing-xs);
    transition: transform 0.3s ease;
}

.nav-link:hover i {
    transform: scale(1.1);
}

.navbar-toggler {
    border: none;
    padding: var(--spacing-xs);
    border-radius: var(--radius-sm);
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
}

.navbar-toggler:focus {
    box-shadow: 0 0 0 2px rgba(103, 126, 234, 0.5);
}

.navbar-toggler-icon {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28255, 255, 255, 0.8%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='m4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
}

/* Modern Glass Morphism Cards */
.card {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-lg);
    box-shadow: var(--glass-shadow);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    margin-bottom: var(--spacing-xl);
    overflow: hidden;
    position: relative;
}

.card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
}

.card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow:
        var(--glass-shadow),
        0 25px 50px rgba(0, 0, 0, 0.2),
        var(--shadow-glow);
    border-color: rgba(103, 126, 234, 0.3);
}

.card-header {
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.1) 0%,
        rgba(255, 255, 255, 0.05) 100%);
    border-bottom: 1px solid var(--glass-border);
    font-weight: 600;
    color: var(--text-primary);
    border-radius: var(--radius-lg) var(--radius-lg) 0 0 !important;
    padding: var(--spacing-lg);
    position: relative;
}

.card-header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(103, 126, 234, 0.5), transparent);
}

.card-body {
    padding: var(--spacing-xl);
    color: var(--text-primary);
}

.card-title {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
    font-size: 1.1rem;
}

/* Modern Summary Cards with Advanced Gradients */
.card.bg-success {
    background: var(--success-gradient) !important;
    border: 1px solid rgba(79, 172, 254, 0.3);
    position: relative;
    overflow: hidden;
}

.card.bg-danger {
    background: var(--danger-gradient) !important;
    border: 1px solid rgba(250, 112, 154, 0.3);
    position: relative;
    overflow: hidden;
}

.card.bg-info {
    background: var(--info-gradient) !important;
    border: 1px solid rgba(168, 237, 234, 0.3);
    position: relative;
    overflow: hidden;
}

.card.bg-warning {
    background: var(--warning-gradient) !important;
    border: 1px solid rgba(67, 233, 123, 0.3);
    position: relative;
    overflow: hidden;
}

/* Animated Background for Summary Cards */
.card.bg-success::before,
.card.bg-danger::before,
.card.bg-info::before,
.card.bg-warning::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    animation: float 6s ease-in-out infinite;
    pointer-events: none;
}

@keyframes float {
    0%, 100% { transform: translate(0, 0) rotate(0deg); }
    33% { transform: translate(30px, -30px) rotate(120deg); }
    66% { transform: translate(-20px, 20px) rotate(240deg); }
}

/* Enhanced Card Content */
.card.bg-success .card-body,
.card.bg-danger .card-body,
.card.bg-info .card-body,
.card.bg-warning .card-body {
    position: relative;
    z-index: 2;
    backdrop-filter: blur(10px);
}

.card.bg-success h4,
.card.bg-danger h4,
.card.bg-info h4,
.card.bg-warning h4 {
    font-weight: 800;
    font-size: 2rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    margin-bottom: var(--spacing-xs);
}

.card.bg-success h6,
.card.bg-danger h6,
.card.bg-info h6,
.card.bg-warning h6 {
    font-weight: 600;
    opacity: 0.9;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 0.85rem;
}

/* Icon Styling in Summary Cards */
.card.bg-success .fa-2x,
.card.bg-danger .fa-2x,
.card.bg-info .fa-2x,
.card.bg-warning .fa-2x {
    opacity: 0.8;
    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3));
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

/* Modern Forms with Glass Effect */
.form-control {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-md);
    color: var(--text-primary);
    padding: var(--spacing-md);
    font-weight: 500;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.form-control::placeholder {
    color: var(--text-muted);
    opacity: 0.7;
}

.form-control:focus {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(103, 126, 234, 0.5);
    box-shadow:
        0 0 0 3px rgba(103, 126, 234, 0.1),
        0 0 20px rgba(103, 126, 234, 0.2);
    outline: none;
    transform: translateY(-2px);
}

.form-control:hover {
    border-color: rgba(255, 255, 255, 0.3);
    background: rgba(255, 255, 255, 0.08);
}

.form-label {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    opacity: 0.9;
}

.form-select {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-md);
    color: var(--text-primary);
    padding: var(--spacing-md);
    font-weight: 500;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23ffffff' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m2 5 6 6 6-6'/%3e%3c/svg%3e");
}

.form-select:focus {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(103, 126, 234, 0.5);
    box-shadow:
        0 0 0 3px rgba(103, 126, 234, 0.1),
        0 0 20px rgba(103, 126, 234, 0.2);
    outline: none;
}

textarea.form-control {
    resize: vertical;
    min-height: 100px;
}

/* Modern Buttons with Advanced Effects */
.btn {
    border-radius: var(--radius-md);
    font-weight: 600;
    padding: var(--spacing-md) var(--spacing-xl);
    border: none;
    position: relative;
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 0.85rem;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.btn:hover::before {
    left: 100%;
}

.btn:hover {
    transform: translateY(-3px) scale(1.02);
    box-shadow:
        0 10px 25px rgba(0, 0, 0, 0.2),
        0 0 20px rgba(103, 126, 234, 0.3);
}

.btn:active {
    transform: translateY(-1px) scale(0.98);
}

.btn-primary {
    background: var(--primary-gradient);
    box-shadow: 0 4px 15px rgba(103, 126, 234, 0.3);
}

.btn-primary:hover {
    box-shadow:
        0 10px 25px rgba(103, 126, 234, 0.4),
        0 0 30px rgba(103, 126, 234, 0.5);
}

.btn-success {
    background: var(--success-gradient);
    box-shadow: 0 4px 15px rgba(79, 172, 254, 0.3);
}

.btn-success:hover {
    box-shadow:
        0 10px 25px rgba(79, 172, 254, 0.4),
        0 0 30px rgba(79, 172, 254, 0.5);
}

.btn-danger {
    background: var(--danger-gradient);
    box-shadow: 0 4px 15px rgba(250, 112, 154, 0.3);
}

.btn-danger:hover {
    box-shadow:
        0 10px 25px rgba(250, 112, 154, 0.4),
        0 0 30px rgba(250, 112, 154, 0.5);
}

.btn-warning {
    background: var(--warning-gradient);
    color: var(--bg-primary) !important;
    box-shadow: 0 4px 15px rgba(67, 233, 123, 0.3);
}

.btn-warning:hover {
    color: var(--bg-primary) !important;
    box-shadow:
        0 10px 25px rgba(67, 233, 123, 0.4),
        0 0 30px rgba(67, 233, 123, 0.5);
}

.btn-info {
    background: var(--info-gradient);
    color: var(--bg-primary) !important;
    box-shadow: 0 4px 15px rgba(168, 237, 234, 0.3);
}

.btn-info:hover {
    color: var(--bg-primary) !important;
    box-shadow:
        0 10px 25px rgba(168, 237, 234, 0.4),
        0 0 30px rgba(168, 237, 234, 0.5);
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
    border: 1px solid var(--glass-border);
    backdrop-filter: blur(10px);
}

.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.2);
    color: var(--text-primary);
    border-color: rgba(255, 255, 255, 0.3);
}

/* Small Buttons */
.btn-sm {
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: 0.75rem;
    border-radius: var(--radius-sm);
}

/* Button Groups */
.btn-group .btn {
    margin: 0 2px;
}

/* Modern Tables with Glass Effect */
.table-responsive {
    border-radius: var(--radius-lg);
    overflow: hidden;
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
}

.table {
    color: var(--text-primary);
    margin-bottom: 0;
    background: transparent;
}

.table thead th {
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.1) 0%,
        rgba(255, 255, 255, 0.05) 100%);
    border-bottom: 1px solid var(--glass-border);
    font-weight: 700;
    color: var(--text-primary);
    padding: var(--spacing-lg);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 0.85rem;
    position: relative;
}

.table thead th::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(103, 126, 234, 0.5), transparent);
}

.table tbody tr {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.table tbody tr:hover {
    background: rgba(255, 255, 255, 0.05);
    transform: scale(1.01);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.table tbody td {
    padding: var(--spacing-lg);
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    vertical-align: middle;
}

.table tbody tr:last-child td {
    border-bottom: none;
}

/* Table Badges */
.badge {
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-weight: 600;
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.badge.bg-primary {
    background: var(--primary-gradient) !important;
}

.badge.bg-success {
    background: var(--success-gradient) !important;
}

.badge.bg-danger {
    background: var(--danger-gradient) !important;
}

.badge.bg-warning {
    background: var(--warning-gradient) !important;
    color: var(--bg-primary) !important;
}

.badge.bg-info {
    background: var(--info-gradient) !important;
    color: var(--bg-primary) !important;
}

/* Action Buttons */
.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
    margin: 0 2px;
}

/* Advanced Animations and Transitions */
.content-section {
    animation: slideInUp 0.8s cubic-bezier(0.4, 0, 0.2, 1);
    opacity: 0;
    animation-fill-mode: forwards;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(60px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* Staggered Animation for Cards */
.card:nth-child(1) { animation-delay: 0.1s; }
.card:nth-child(2) { animation-delay: 0.2s; }
.card:nth-child(3) { animation-delay: 0.3s; }
.card:nth-child(4) { animation-delay: 0.4s; }

.card {
    animation: cardSlideIn 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;
    opacity: 0;
    transform: translateY(30px);
}

@keyframes cardSlideIn {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Floating Elements */
.floating {
    animation: floating 3s ease-in-out infinite;
}

@keyframes floating {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

/* Glow Effect */
.glow {
    animation: glow 2s ease-in-out infinite alternate;
}

@keyframes glow {
    from {
        box-shadow: 0 0 20px rgba(103, 126, 234, 0.3);
    }
    to {
        box-shadow: 0 0 30px rgba(103, 126, 234, 0.6);
    }
}

/* Ripple Effect */
.ripple {
    position: relative;
    overflow: hidden;
}

.ripple::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s;
}

.ripple:active::before {
    width: 300px;
    height: 300px;
}

/* Smooth Scrolling */
html {
    scroll-behavior: smooth;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--bg-secondary);
    border-radius: var(--radius-sm);
}

::-webkit-scrollbar-thumb {
    background: var(--primary-gradient);
    border-radius: var(--radius-sm);
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #5a67d8 0%, #667eea 100%);
}

/* Charts */
canvas {
    max-height: 400px;
}

/* Advanced Responsive Design */
@media (max-width: 1200px) {
    .card-body {
        padding: var(--spacing-lg);
    }

    .modal-dialog {
        margin: var(--spacing-md);
    }
}

@media (max-width: 992px) {
    .navbar-brand {
        font-size: 1.5rem;
    }

    .card {
        margin-bottom: var(--spacing-lg);
    }

    .btn {
        padding: var(--spacing-sm) var(--spacing-lg);
    }
}

@media (max-width: 768px) {
    :root {
        --spacing-xl: 1.5rem;
        --spacing-2xl: 2rem;
    }

    .navbar-brand {
        font-size: 1.3rem;
    }

    .card-body {
        padding: var(--spacing-md);
    }

    .table-responsive {
        font-size: 0.85rem;
    }

    .btn {
        font-size: 0.8rem;
        padding: var(--spacing-sm) var(--spacing-md);
    }

    .modal-dialog {
        margin: var(--spacing-sm);
    }

    .modal-content {
        border-radius: var(--radius-lg);
    }

    .form-control,
    .form-select {
        padding: var(--spacing-sm);
    }

    /* Stack summary cards on mobile */
    .row .col-md-3 {
        margin-bottom: var(--spacing-md);
    }
}

@media (max-width: 576px) {
    .container-fluid {
        padding: var(--spacing-sm);
    }

    .card {
        border-radius: var(--radius-md);
    }

    .btn {
        width: 100%;
        margin-bottom: var(--spacing-xs);
    }

    .btn-group .btn {
        width: auto;
        margin: 0 2px;
    }

    .table thead th,
    .table tbody td {
        padding: var(--spacing-sm);
        font-size: 0.8rem;
    }

    .navbar-nav {
        text-align: center;
    }

    .nav-link {
        margin: var(--spacing-xs) 0;
    }
}

/* Ultra-wide screens */
@media (min-width: 1400px) {
    .container-fluid {
        max-width: 1320px;
        margin: 0 auto;
    }
}

/* High DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .card {
        border-width: 0.5px;
    }

    .table thead th {
        border-bottom-width: 0.5px;
    }
}

/* Reduced motion for accessibility */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Dark mode enhancements */
@media (prefers-color-scheme: dark) {
    :root {
        --glass-bg: rgba(255, 255, 255, 0.03);
        --glass-border: rgba(255, 255, 255, 0.1);
    }
}

/* Print optimizations */
@media print {
    body {
        background: white !important;
        color: black !important;
    }

    .navbar,
    .btn,
    .modal,
    .alert {
        display: none !important;
    }

    .card {
        border: 1px solid #000 !important;
        box-shadow: none !important;
        background: white !important;
        break-inside: avoid;
    }

    .table {
        color: black !important;
    }

    .table thead th {
        background: #f0f0f0 !important;
        color: black !important;
    }
}

/* Loading Spinner */
.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* Alert Styles */
.alert {
    border-radius: 8px;
    border: none;
}

.alert-success {
    background: linear-gradient(135deg, #d4edda, #c3e6cb);
    color: #155724;
}

.alert-danger {
    background: linear-gradient(135deg, #f8d7da, #f5c6cb);
    color: #721c24;
}

.alert-info {
    background: linear-gradient(135deg, #d1ecf1, #bee5eb);
    color: #0c5460;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #555;
}

/* Print Styles */
@media print {
    .navbar,
    .btn,
    .card-header {
        display: none !important;
    }
    
    .card {
        box-shadow: none !important;
        border: 1px solid #000 !important;
    }
    
    body {
        background: white !important;
    }
}

/* Utility Classes */
.text-currency {
    font-weight: 600;
    color: #28a745;
}

.text-expense {
    font-weight: 600;
    color: #dc3545;
}

.badge-category {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
}

/* Advanced Modal Design */
.modal-backdrop {
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.modal-content {
    background: var(--glass-bg);
    backdrop-filter: blur(30px);
    -webkit-backdrop-filter: blur(30px);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-xl);
    box-shadow:
        var(--glass-shadow),
        0 25px 50px rgba(0, 0, 0, 0.3);
    overflow: hidden;
    position: relative;
}

.modal-content::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
}

.modal-header {
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.1) 0%,
        rgba(255, 255, 255, 0.05) 100%);
    border-bottom: 1px solid var(--glass-border);
    border-radius: var(--radius-xl) var(--radius-xl) 0 0;
    padding: var(--spacing-xl);
    position: relative;
}

.modal-header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(103, 126, 234, 0.5), transparent);
}

.modal-title {
    color: var(--text-primary);
    font-weight: 700;
    font-size: 1.5rem;
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.modal-body {
    padding: var(--spacing-xl);
    color: var(--text-primary);
}

.modal-footer {
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.05) 0%,
        rgba(255, 255, 255, 0.02) 100%);
    border-top: 1px solid var(--glass-border);
    border-radius: 0 0 var(--radius-xl) var(--radius-xl);
    padding: var(--spacing-xl);
    position: relative;
}

.modal-footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(103, 126, 234, 0.3), transparent);
}

.btn-close {
    background: none;
    border: none;
    color: var(--text-primary);
    opacity: 0.7;
    transition: all 0.3s ease;
    padding: var(--spacing-sm);
    border-radius: var(--radius-sm);
}

.btn-close:hover {
    opacity: 1;
    background: rgba(255, 255, 255, 0.1);
    transform: scale(1.1);
}

/* Enhanced Alerts */
.alert {
    border: none;
    border-radius: var(--radius-lg);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-left: 4px solid;
    position: relative;
    overflow: hidden;
    animation: slideInDown 0.5s ease-out;
}

@keyframes slideInDown {
    from {
        opacity: 0;
        transform: translateY(-30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.alert::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
}

.alert-success {
    background: rgba(79, 172, 254, 0.1);
    color: #4facfe;
    border-left-color: #4facfe;
}

.alert-danger {
    background: rgba(250, 112, 154, 0.1);
    color: #fa709a;
    border-left-color: #fa709a;
}

.alert-warning {
    background: rgba(67, 233, 123, 0.1);
    color: #43e97b;
    border-left-color: #43e97b;
}

.alert-info {
    background: rgba(168, 237, 234, 0.1);
    color: #a8edea;
    border-left-color: #a8edea;
}

/* Status Indicators */
.status-paid {
    color: #28a745;
    font-weight: 600;
}

.status-pending {
    color: #ffc107;
    font-weight: 600;
}

.status-overdue {
    color: #dc3545;
    font-weight: 600;
}

/* Dashboard Enhancements */
.dashboard-card {
    background: linear-gradient(135deg, #ffffff, #f8f9fa);
    border-left: 4px solid #0d6efd;
}

.dashboard-card.income {
    border-left-color: #28a745;
}

.dashboard-card.expense {
    border-left-color: #dc3545;
}

.dashboard-card.profit {
    border-left-color: #17a2b8;
}

.dashboard-card.invoice {
    border-left-color: #ffc107;
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Enhanced Form Validation */
.form-control.is-invalid {
    border-color: #dc3545;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

.form-control.is-valid {
    border-color: #28a745;
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}

/* Enhanced Tables */
.table-hover tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.075);
}

/* Enhanced Modals */
.modal-backdrop {
    background-color: rgba(0, 0, 0, 0.6);
}

/* Print Optimizations */
@media print {
    .no-print {
        display: none !important;
    }

    .print-only {
        display: block !important;
    }

    .card {
        break-inside: avoid;
    }

    .table {
        font-size: 12px;
    }
}

/* Accessibility Improvements */
.btn:focus,
.form-control:focus {
    outline: 2px solid #0d6efd;
    outline-offset: 2px;
}

/* Dark Mode Support (Optional) */
@media (prefers-color-scheme: dark) {
    body {
        background-color: #1a1a1a;
        color: #ffffff;
    }

    .card {
        background-color: #2d2d2d;
        border-color: #404040;
    }

    .table {
        color: #ffffff;
    }

    .table thead th {
        background-color: #404040;
        border-color: #555555;
    }
}

/* Enhanced Animations */
.fade-in {
    animation: fadeInUp 0.5s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Enhanced Status Indicators */
.status-indicator {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-left: 5px;
}

.status-indicator.success {
    background-color: #28a745;
}

.status-indicator.warning {
    background-color: #ffc107;
}

.status-indicator.danger {
    background-color: #dc3545;
}
