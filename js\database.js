// Local Database Management System
// Using localStorage for data persistence

class AccountingDatabase {
    constructor() {
        this.storageKeys = {
            income: 'accounting_income_data',
            expenses: 'accounting_expense_data',
            invoices: 'accounting_invoice_data',
            customers: 'accounting_customer_data',
            settings: 'accounting_settings_data',
            categories: 'accounting_categories_data'
        };
        
        this.initializeDefaultData();
    }

    // Initialize default categories and settings
    initializeDefaultData() {
        if (!this.getCategories()) {
            const defaultCategories = {
                income: [
                    { id: 1, name: 'مبيعات', color: '#28a745' },
                    { id: 2, name: 'خدمات', color: '#17a2b8' },
                    { id: 3, name: 'استثمارات', color: '#6f42c1' },
                    { id: 4, name: 'عمولات', color: '#fd7e14' },
                    { id: 5, name: 'أخرى', color: '#6c757d' }
                ],
                expenses: [
                    { id: 1, name: 'مكتب وإدارة', color: '#dc3545' },
                    { id: 2, name: 'مواصلات', color: '#ffc107' },
                    { id: 3, name: 'طعام وضيافة', color: '#20c997' },
                    { id: 4, name: 'تسويق وإعلان', color: '#e83e8c' },
                    { id: 5, name: 'رواتب', color: '#6f42c1' },
                    { id: 6, name: 'إيجار', color: '#fd7e14' },
                    { id: 7, name: 'كهرباء وماء', color: '#17a2b8' },
                    { id: 8, name: 'صيانة', color: '#28a745' },
                    { id: 9, name: 'أخرى', color: '#6c757d' }
                ]
            };
            this.saveCategories(defaultCategories);
        }

        if (!this.getSettings()) {
            const defaultSettings = {
                currency: 'SAR',
                currencySymbol: 'ر.س',
                dateFormat: 'dd/mm/yyyy',
                companyName: 'شركتي',
                companyAddress: '',
                companyPhone: '',
                companyEmail: '',
                taxNumber: '',
                taxRate: 15
            };
            this.saveSettings(defaultSettings);
        }
    }

    // Generic storage methods
    saveData(key, data) {
        try {
            localStorage.setItem(key, JSON.stringify(data));
            return true;
        } catch (error) {
            console.error('Error saving data:', error);
            return false;
        }
    }

    getData(key) {
        try {
            const data = localStorage.getItem(key);
            return data ? JSON.parse(data) : null;
        } catch (error) {
            console.error('Error loading data:', error);
            return null;
        }
    }

    // Income methods
    saveIncome(incomeArray) {
        return this.saveData(this.storageKeys.income, incomeArray);
    }

    getIncome() {
        return this.getData(this.storageKeys.income) || [];
    }

    addIncome(incomeItem) {
        const incomeArray = this.getIncome();
        incomeItem.id = this.generateId();
        incomeItem.createdAt = new Date().toISOString();
        incomeArray.push(incomeItem);
        return this.saveIncome(incomeArray);
    }

    updateIncome(id, updatedItem) {
        const incomeArray = this.getIncome();
        const index = incomeArray.findIndex(item => item.id === id);
        if (index !== -1) {
            incomeArray[index] = { ...incomeArray[index], ...updatedItem, updatedAt: new Date().toISOString() };
            return this.saveIncome(incomeArray);
        }
        return false;
    }

    deleteIncome(id) {
        const incomeArray = this.getIncome();
        const filteredArray = incomeArray.filter(item => item.id !== id);
        return this.saveIncome(filteredArray);
    }

    // Expense methods
    saveExpenses(expenseArray) {
        return this.saveData(this.storageKeys.expenses, expenseArray);
    }

    getExpenses() {
        return this.getData(this.storageKeys.expenses) || [];
    }

    addExpense(expenseItem) {
        const expenseArray = this.getExpenses();
        expenseItem.id = this.generateId();
        expenseItem.createdAt = new Date().toISOString();
        expenseArray.push(expenseItem);
        return this.saveExpenses(expenseArray);
    }

    updateExpense(id, updatedItem) {
        const expenseArray = this.getExpenses();
        const index = expenseArray.findIndex(item => item.id === id);
        if (index !== -1) {
            expenseArray[index] = { ...expenseArray[index], ...updatedItem, updatedAt: new Date().toISOString() };
            return this.saveExpenses(expenseArray);
        }
        return false;
    }

    deleteExpense(id) {
        const expenseArray = this.getExpenses();
        const filteredArray = expenseArray.filter(item => item.id !== id);
        return this.saveExpenses(filteredArray);
    }

    // Invoice methods
    saveInvoices(invoiceArray) {
        return this.saveData(this.storageKeys.invoices, invoiceArray);
    }

    getInvoices() {
        return this.getData(this.storageKeys.invoices) || [];
    }

    addInvoice(invoiceItem) {
        const invoiceArray = this.getInvoices();
        invoiceItem.id = this.generateId();
        invoiceItem.invoiceNumber = this.generateInvoiceNumber();
        invoiceItem.createdAt = new Date().toISOString();
        invoiceArray.push(invoiceItem);
        return this.saveInvoices(invoiceArray);
    }

    updateInvoice(id, updatedItem) {
        const invoiceArray = this.getInvoices();
        const index = invoiceArray.findIndex(item => item.id === id);
        if (index !== -1) {
            invoiceArray[index] = { ...invoiceArray[index], ...updatedItem, updatedAt: new Date().toISOString() };
            return this.saveInvoices(invoiceArray);
        }
        return false;
    }

    deleteInvoice(id) {
        const invoiceArray = this.getInvoices();
        const filteredArray = invoiceArray.filter(item => item.id !== id);
        return this.saveInvoices(filteredArray);
    }

    // Customer methods
    saveCustomers(customerArray) {
        return this.saveData(this.storageKeys.customers, customerArray);
    }

    getCustomers() {
        return this.getData(this.storageKeys.customers) || [];
    }

    addCustomer(customerItem) {
        const customerArray = this.getCustomers();
        customerItem.id = this.generateId();
        customerItem.createdAt = new Date().toISOString();
        customerArray.push(customerItem);
        return this.saveCustomers(customerArray);
    }

    // Categories methods
    saveCategories(categories) {
        return this.saveData(this.storageKeys.categories, categories);
    }

    getCategories() {
        return this.getData(this.storageKeys.categories);
    }

    // Settings methods
    saveSettings(settings) {
        return this.saveData(this.storageKeys.settings, settings);
    }

    getSettings() {
        return this.getData(this.storageKeys.settings);
    }

    // Utility methods
    generateId() {
        return Date.now() + Math.random().toString(36).substr(2, 9);
    }

    generateInvoiceNumber() {
        const invoices = this.getInvoices();
        const currentYear = new Date().getFullYear();
        const yearInvoices = invoices.filter(inv => 
            new Date(inv.createdAt).getFullYear() === currentYear
        );
        const nextNumber = yearInvoices.length + 1;
        return `INV-${currentYear}-${nextNumber.toString().padStart(4, '0')}`;
    }

    // Data analysis methods
    getTotalIncome(startDate = null, endDate = null) {
        const income = this.getIncome();
        const filtered = this.filterByDateRange(income, startDate, endDate);
        return filtered.reduce((total, item) => total + parseFloat(item.amount), 0);
    }

    getTotalExpenses(startDate = null, endDate = null) {
        const expenses = this.getExpenses();
        const filtered = this.filterByDateRange(expenses, startDate, endDate);
        return filtered.reduce((total, item) => total + parseFloat(item.amount), 0);
    }

    getNetProfit(startDate = null, endDate = null) {
        return this.getTotalIncome(startDate, endDate) - this.getTotalExpenses(startDate, endDate);
    }

    filterByDateRange(data, startDate, endDate) {
        if (!startDate && !endDate) return data;
        
        return data.filter(item => {
            const itemDate = new Date(item.date);
            const start = startDate ? new Date(startDate) : new Date('1900-01-01');
            const end = endDate ? new Date(endDate) : new Date('2100-12-31');
            return itemDate >= start && itemDate <= end;
        });
    }

    // Export/Import methods
    exportData() {
        const data = {
            income: this.getIncome(),
            expenses: this.getExpenses(),
            invoices: this.getInvoices(),
            customers: this.getCustomers(),
            categories: this.getCategories(),
            settings: this.getSettings(),
            exportDate: new Date().toISOString()
        };
        return JSON.stringify(data, null, 2);
    }

    importData(jsonData) {
        try {
            const data = JSON.parse(jsonData);
            
            if (data.income) this.saveIncome(data.income);
            if (data.expenses) this.saveExpenses(data.expenses);
            if (data.invoices) this.saveInvoices(data.invoices);
            if (data.customers) this.saveCustomers(data.customers);
            if (data.categories) this.saveCategories(data.categories);
            if (data.settings) this.saveSettings(data.settings);
            
            return true;
        } catch (error) {
            console.error('Error importing data:', error);
            return false;
        }
    }

    // Clear all data
    clearAllData() {
        Object.values(this.storageKeys).forEach(key => {
            localStorage.removeItem(key);
        });
        this.initializeDefaultData();
    }

    // Get storage usage
    getStorageUsage() {
        let totalSize = 0;
        Object.values(this.storageKeys).forEach(key => {
            const data = localStorage.getItem(key);
            if (data) {
                totalSize += data.length;
            }
        });
        return {
            bytes: totalSize,
            kb: (totalSize / 1024).toFixed(2),
            mb: (totalSize / (1024 * 1024)).toFixed(2)
        };
    }
}

// Create global database instance
const db = new AccountingDatabase();
