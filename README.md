# نظام المحاسبة المتكامل

نظام محاسبي شامل مطور بتقنيات الويب الحديثة لإدارة الأعمال المالية بكفاءة عالية.

## المميزات الرئيسية

### 📊 لوحة التحكم
- ملخص شامل للوضع المالي
- عرض الإيرادات والمصروفات والأرباح الصافية
- رسوم بيانية تفاعلية للأداء المالي
- إحصائيات الفواتير والعمليات

### 💰 إدارة الإيرادات
- إضافة وتعديل وحذف الإيرادات
- تصنيف الإيرادات حسب المصدر
- تتبع تاريخي للإيرادات
- تحليل الإيرادات حسب الفترة الزمنية

### 💸 إدارة المصروفات
- تسجيل المصروفات بالتفصيل
- تصنيف المصروفات حسب النوع
- إضافة ملاحظات للمصروفات
- تحليل أنماط الإنفاق

### 🧾 إدارة الفواتير
- إنشاء فواتير احترافية
- حساب الضرائب تلقائياً (15%)
- تتبع حالة الفواتير (معلقة/مدفوعة/متأخرة)
- إدارة بيانات العملاء
- طباعة وتصدير الفواتير

### 📈 التقارير والإحصائيات
- تقارير مفصلة للإيرادات والمصروفات
- تقارير الفواتير وحالات الدفع
- رسوم بيانية للاتجاهات المالية
- تحليل البيانات حسب الفترة الزمنية
- مقارنة الأداء الشهري والسنوي

### 📤 التصدير والطباعة
- تصدير التقارير إلى Excel
- تصدير التقارير إلى PDF
- طباعة الفواتير
- نسخ احتياطي للبيانات

## التقنيات المستخدمة

- **HTML5**: هيكل الصفحات
- **CSS3**: التصميم والتنسيق
- **Bootstrap 5**: إطار العمل للتصميم المتجاوب
- **JavaScript**: البرمجة والتفاعل
- **Chart.js**: الرسوم البيانية
- **LocalStorage**: تخزين البيانات محلياً
- **Font Awesome**: الأيقونات
- **SheetJS**: تصدير Excel
- **jsPDF**: تصدير PDF

## متطلبات التشغيل

- متصفح ويب حديث (Chrome, Firefox, Safari, Edge)
- دعم JavaScript
- دعم LocalStorage

## طريقة التشغيل

1. قم بتحميل الملفات
2. افتح ملف `index.html` في المتصفح
3. ابدأ في استخدام النظام فوراً

## هيكل المشروع

```
accounting-system/
├── index.html              # الصفحة الرئيسية
├── css/
│   └── style.css          # ملف التنسيق المخصص
├── js/
│   ├── app.js             # الوظائف الرئيسية
│   └── database.js        # إدارة قاعدة البيانات
└── README.md              # دليل المستخدم
```

## الاستخدام

### إضافة إيراد جديد
1. انتقل إلى قسم "الإيرادات"
2. املأ بيانات الإيراد (الوصف، المبلغ، التصنيف، التاريخ)
3. اضغط "إضافة الإيراد"

### إضافة مصروف جديد
1. انتقل إلى قسم "المصروفات"
2. املأ بيانات المصروف (الوصف، المبلغ، التصنيف، التاريخ، الملاحظات)
3. اضغط "إضافة المصروف"

### إنشاء فاتورة جديدة
1. انتقل إلى قسم "الفواتير"
2. اضغط "إنشاء فاتورة جديدة"
3. املأ بيانات العميل
4. أضف عناصر الفاتورة
5. احفظ الفاتورة

### إنشاء التقارير
1. انتقل إلى قسم "التقارير"
2. حدد الفترة الزمنية
3. اختر نوع التقرير
4. اضغط "إنشاء التقرير"
5. يمكنك تصدير التقرير إلى Excel أو PDF

## المميزات التقنية

- **تصميم متجاوب**: يعمل على جميع الأجهزة والشاشات
- **تخزين محلي**: البيانات محفوظة في المتصفح
- **واجهة عربية**: دعم كامل للغة العربية
- **رسوم بيانية تفاعلية**: عرض البيانات بصرياً
- **تصدير متعدد الصيغ**: Excel و PDF
- **حسابات تلقائية**: الضرائب والإجماليات

## الأمان والخصوصية

- جميع البيانات محفوظة محلياً في المتصفح
- لا يتم إرسال أي بيانات إلى خوادم خارجية
- يمكن عمل نسخ احتياطية يدوياً
- البيانات آمنة ومحمية

## الدعم والتطوير

هذا النظام قابل للتطوير والتخصيص حسب احتياجات العمل. يمكن إضافة المزيد من المميزات مثل:

- ربط قواعد بيانات خارجية
- نظام المستخدمين والصلاحيات
- تكامل مع أنظمة الدفع
- تطبيق جوال
- تقارير أكثر تفصيلاً

## الترخيص

هذا المشروع مطور لأغراض تعليمية وتجارية. يمكن استخدامه وتعديله حسب الحاجة.

## معلومات الاتصال

للاستفسارات والدعم التقني، يرجى التواصل معنا.

---

**تم تطوير هذا النظام باستخدام أحدث تقنيات الويب لضمان الأداء الأمثل وسهولة الاستخدام.**
